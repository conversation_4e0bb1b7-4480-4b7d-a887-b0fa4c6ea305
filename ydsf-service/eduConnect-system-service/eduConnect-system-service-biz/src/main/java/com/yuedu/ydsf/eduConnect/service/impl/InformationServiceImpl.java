package com.yuedu.ydsf.eduConnect.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.ydsf.common.core.constant.enums.BizErrorCodeEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.file.domain.OssStsResult;
import com.yuedu.ydsf.eduConnect.api.constant.IsRootEnum;
import com.yuedu.ydsf.eduConnect.api.dto.InformationAuthStoreDTO;
import com.yuedu.ydsf.eduConnect.entity.InformationAuthStore;
import com.yuedu.ydsf.eduConnect.entity.InformationResource;
import com.yuedu.ydsf.eduConnect.manager.InformationManager;
import com.yuedu.ydsf.eduConnect.mapper.InformationAuthStoreMapper;
import com.yuedu.ydsf.eduConnect.mapper.InformationResourceMapper;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.eduConnect.mapper.InformationMapper;
import com.yuedu.ydsf.eduConnect.service.InformationService;
import com.yuedu.ydsf.eduConnect.api.query.InformationQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationVO;
import com.yuedu.ydsf.eduConnect.entity.Information;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;
import java.util.List;


/**
 * 资料管理服务层
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
@Service
@AllArgsConstructor
public class InformationServiceImpl extends ServiceImpl<InformationMapper, Information>
    implements InformationService {

    private final InformationManager informationManager;


    /**
     * 资料管理分页查询
     *
     * @param page             分页对象
     * @param informationQuery 资料管理
     * @return IPage 分页结果
     */
    @Override
    public IPage<InformationVO> page(Page page, InformationQuery informationQuery) {
        return informationManager.fillData(baseMapper.page(page, informationQuery));
    }


    /**
     * 根据ID获得资料管理信息
     *
     * @param id id
     * @return InformationVO 详细信息
     */
    @Override
    public InformationVO getInfoById(Serializable id) {
        return Optional.ofNullable(getById(id))
            .map(entity -> {
                InformationVO informationVO = new InformationVO();
                BeanUtils.copyProperties(entity, informationVO);
                if(IsRootEnum.IS_ROOT_0.code.equals(informationVO.getIsRoot())){
                    informationVO.setParent(getInfoById(entity.getPid()));
                }
                return informationVO;
            })
            .orElseThrow(() -> new BizException("未查询到记录信息！"));
    }


    /**
     * 新增资料管理
     *
     * @param informationDTO 资料管理
     * @return boolean 执行结果
     */
    @Override
    public InformationVO add(InformationDTO informationDTO) {

        if (Objects.isNull(informationDTO.getPid())) {
            if (CollectionUtil.isNotEmpty(list(Wrappers.<Information>lambdaQuery()
                .eq(Information::getIsRoot, IsRootEnum.IS_ROOT_1.code)
                .eq(Information::getContentsName, informationDTO.getContentsName())))) {
                throw new BizException("已存在相同名称目录！");
            }
            informationDTO.setIsRoot(IsRootEnum.IS_ROOT_1.code);
        } else {
            if (CollectionUtil.isNotEmpty(list(Wrappers.<Information>lambdaQuery()
                .eq(Information::getContentsName, informationDTO.getContentsName())
                .eq(Information::getPid, informationDTO.getPid())))) {
                throw new BizException("当前目录下已存在相同名称目录！");
            }
            informationDTO.setIsRoot(IsRootEnum.IS_ROOT_0.code);
        }

        Information information = new Information();
        BeanUtils.copyProperties(informationDTO, information);
        save(information);
        return BeanUtil.copyProperties(information, InformationVO.class);
    }


    /**
     * 修改资料管理
     *
     * @param informationDTO 资料管理
     * @return boolean 执行结果
     */
    @Override
    public boolean edit(InformationDTO informationDTO) {
        Information oldInformation = getById(informationDTO.getId());
        if (Objects.isNull(oldInformation)) {
            throw new BizException("未找到该记录！");
        }

        if (IsRootEnum.IS_ROOT_1.code.equals(oldInformation.getIsRoot())) {
            list(Wrappers.<Information>lambdaQuery()
                .eq(Information::getIsRoot, IsRootEnum.IS_ROOT_1.code)
                .eq(Information::getContentsName, informationDTO.getContentsName())).forEach(s -> {
                if (!s.getId().equals(informationDTO.getId())) {
                    throw new BizException("已存在相同名称目录！");
                }
            });
        } else {
            list(Wrappers.<Information>lambdaQuery()
                .eq(Information::getContentsName, informationDTO.getContentsName())
                .eq(Information::getPid, oldInformation.getPid())).forEach(s -> {
                if (!s.getId().equals(informationDTO.getId())) {
                    throw new BizException("当前目录下已存在相同名称目录！");
                }
            });
        }

        Information information = new Information();
        information.setId(oldInformation.getId());
        information.setContentsName(informationDTO.getContentsName());
        return updateById(information);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Serializable id) {
        Information oldInformation = getById(id);
        if (Objects.isNull(oldInformation)) {
            throw new BizException("未找到该记录！");
        }

        if (IsRootEnum.IS_ROOT_1.code.equals(oldInformation.getIsRoot())) {
            list(Wrappers.<Information>lambdaQuery()
                .eq(Information::getPid, oldInformation.getId())).forEach(s -> {
                throw new BizException("当前目录下存在子目录，请先删除子目录！");
            });

//            if(informationManager.checkAuht(oldInformation)){
//                throw new BizException("当前目录下存在授权，请先删除授权！");
//            }

            informationManager.deleteAuth(oldInformation);

        } else {

            if(informationManager.checkResource(oldInformation)){
                throw new BizException("当前目录下存在资源，请先删除资源！");
            }
        }

        return removeById(id);
    }

    @Override
    public List<InformationVO> getChildById(Serializable id) {
        return list(Wrappers.<Information>lambdaQuery().eq(Information::getPid, id).orderByAsc(Information::getCreateTime)).stream()
            .map(s -> {
                InformationVO informationVO = new InformationVO();
                BeanUtils.copyProperties(s, informationVO);
                return informationVO;
            }).toList();
    }

    @Override
    public InformationVO getAuthById(Serializable id) {

        Information oldInformation = getById(id);
        if (Objects.isNull(oldInformation) || !IsRootEnum.IS_ROOT_1.code.equals(oldInformation.getIsRoot())) {
            throw new BizException("未找到该记录信息或者当前目录不是根目录！");
        }
        InformationVO informationVO = new InformationVO();
        BeanUtils.copyProperties(oldInformation, informationVO);
        informationManager.fillAuthData(informationVO);
        return informationVO;
    }

    @Override
    public InformationVO getResourceById(Serializable id) {
        Information oldInformation = getById(id);
        if (Objects.isNull(oldInformation) || !IsRootEnum.IS_ROOT_0.code.equals(oldInformation.getIsRoot())) {
            throw new BizException("未找到该记录信息或者当前目录不是子目录！");
        }

        InformationVO informationVO = new InformationVO();
        BeanUtils.copyProperties(oldInformation, informationVO);
        informationManager.fillResourceData(informationVO);
        return informationVO;
    }

    @Override
    public OssStsResult getInformationUploadStsToken(Long id) {
        Information oldInformation = getById(id);
        if (Objects.isNull(oldInformation) || !IsRootEnum.IS_ROOT_0.code.equals(oldInformation.getIsRoot())) {
            throw new BizException("未找到该记录信息或者当前目录不是子目录！");
        }

        return informationManager.getStsToken(oldInformation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void informationAuthStore(InformationDTO informationDTO) {
        Information oldInformation = getById(informationDTO.getId());
        if (Objects.isNull(oldInformation) || !IsRootEnum.IS_ROOT_1.code.equals(oldInformation.getIsRoot())) {
            throw new BizException("未找到该记录信息或者当前目录不是根目录！");
        }
        informationManager.authStore(informationDTO);
    }


}
