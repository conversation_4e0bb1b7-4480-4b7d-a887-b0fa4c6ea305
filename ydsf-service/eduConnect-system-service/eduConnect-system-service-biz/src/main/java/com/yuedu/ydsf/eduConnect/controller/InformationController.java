package com.yuedu.ydsf.eduConnect.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.file.domain.OssStsResult;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.eduConnect.api.dto.InformationAuthStoreDTO;
import com.yuedu.ydsf.eduConnect.api.valid.InformationValidGroup.InformationAuth;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.eduConnect.service.InformationService;
import com.yuedu.ydsf.eduConnect.api.query.InformationQuery;
import com.yuedu.ydsf.eduConnect.api.dto.InformationDTO;
import com.yuedu.ydsf.eduConnect.api.vo.InformationVO;

import java.io.Serializable;
import java.util.List;

/**
* 资料管理控制层
*
* <AUTHOR>
* @date  2025/07/22
*/

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/information")
@Tag(description = "ss_information" , name = "资料管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class InformationController  {


    private final InformationService informationService;


    /**
    * 资料管理分页查询
    * @param page 分页对象
    * @param informationQuery 资料管理
    * @return R
    */
    @GetMapping("/page" )
    @HasPermission("edusys_information_view")
    @Operation(summary = "分页查询" , description = "资料管理分页查询" )
    public R<IPage<InformationVO>> page(@ParameterObject Page page, @ParameterObject InformationQuery informationQuery) {
        return R.ok(informationService.page(page, informationQuery));
    }



    /**
     * 通过id查询详情
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询资料管理" )
    @GetMapping("/info/{id}" )
    @HasPermission("edusys_information_info")
    public R<InformationVO> getById(@PathVariable Serializable id) {
        return R.ok(informationService.getInfoById(id));
    }


    /**
    * 通过id查询授权列表
    * @param id id
    * @return R
    */
    @Operation(summary = "通过id查询授权列表" , description = "通过id查询授权列表" )
    @GetMapping("/auth/{id}" )
    @HasPermission("edusys_information_find_auth")
    public R<InformationVO> getAuthById(@PathVariable Serializable id) {
        return R.ok(informationService.getAuthById(id));
    }


    /**
     * 通过id查询资源列表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询资源列表" , description = "通过id查询资源列表" )
    @GetMapping("/resource/{id}" )
    @HasPermission("edusys_information_find_resource")
    public R<InformationVO> getResourceById(@PathVariable Serializable id) {
        return R.ok(informationService.getResourceById(id));
    }



    /**
     * 通过id查询子目录
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询子目录" , description = "通过id查询子目录" )
    @GetMapping("/child/{id}" )
    @HasPermission("edusys_information_child")
    public R getChildById(@PathVariable Serializable id) {
        return R.ok(informationService.getChildById(id));
    }




    /**
    * 新增资料管理
    * @param informationDTO 资料管理
    * @return R
    */
    @PostMapping("/save")
    @SysLog("新增资料管理" )
    @HasPermission("edusys_information_save" )
    @Operation(summary = "新增资料管理" , description = "新增资料管理" )
    @Idempotent(info = "操作频繁，请稍后再试！")
    public R add(@Validated(V_A.class) @RequestBody InformationDTO informationDTO) {
         return R.ok(informationService.add(informationDTO));
    }


    /**
    * 修改资料管理
    * @param informationDTO 资料管理
    * @return R
    */
    @PutMapping("/edit")
    @SysLog("修改资料管理" )
    @HasPermission("edusys_information_edit" )
    @Operation(summary = "修改资料管理" , description = "修改资料管理" )
    @Idempotent(key = "#informationDTO.id", info = "操作频繁，请稍后再试！")
    public R edit(@Validated(V_E.class) @RequestBody InformationDTO informationDTO) {
         return R.ok(informationService.edit(informationDTO));
    }



    /**
    * 通过id删除资料管理
    * @return R
    */
    @DeleteMapping("/del/{id}")
    @SysLog("通过id删除资料管理" )
    @HasPermission("edusys_information_del" )
    @Operation(summary = "删除资料管理" , description = "删除资料管理" )
    @Idempotent(key = "#id", info = "操作频繁，请稍后再试！")
    public R delete(@PathVariable("id") Serializable id){
         return R.ok(informationService.delete(id));
    }



    /**
     *  获取资料上传临时token
     *
     * <AUTHOR>
     * @date 2025年07月22日 11时29分
     */
    @GetMapping(value = "/upload/sts/{id}")
    @HasPermission("edusys_information_upload" )
    @Operation(summary = "获取资料上传临时token" , description = "获取资料上传临时token" )
    public R<OssStsResult> informationUploadStsToken(
        @PathVariable("id") @Parameter(description = "目录ID") @NotNull(message = "目录ID") Long id){
        return R.ok(informationService.getInformationUploadStsToken(id));
    }



    /**
     *  授权门店资料
     *
     * <AUTHOR>
     * @date 2025年07月22日 13时37分
     */
    @PutMapping(value = "/auth")
    @HasPermission("edusys_information_auth" )
    @Operation(summary = "授权门店资料" , description = "授权门店资料" )
    @Idempotent(key = "#informationDTO.id", info = "操作频繁，请稍后再试！")
    public R informationAuthStore(@RequestBody @Validated(InformationAuth.class) InformationDTO informationDTO){
        informationService.informationAuthStore(informationDTO);
        return R.ok("授权成功");
    }

}
